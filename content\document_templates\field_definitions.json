{"field_definitions": {"plaintiff_name": {"label": "原告姓名", "type": "text", "required": true, "placeholder": "请输入原告的真实姓名", "validation": "^[\\u4e00-\\u9fa5]{2,10}$"}, "plaintiff_gender": {"label": "原告性别", "type": "select", "required": true, "options": ["男", "女"]}, "plaintiff_birth": {"label": "原告出生日期", "type": "date", "required": true, "placeholder": "YYYY-MM-DD"}, "plaintiff_nation": {"label": "原告民族", "type": "text", "required": true, "placeholder": "如：汉族", "default": "汉族"}, "plaintiff_address": {"label": "原告住址", "type": "textarea", "required": true, "placeholder": "请输入详细住址"}, "plaintiff_phone": {"label": "原告联系电话", "type": "tel", "required": true, "placeholder": "请输入手机号码", "validation": "^1[3-9]\\d{9}$"}, "plaintiff_id": {"label": "原告身份证号", "type": "text", "required": true, "placeholder": "请输入18位身份证号码", "validation": "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$"}, "defendant_name": {"label": "被告姓名", "type": "text", "required": true, "placeholder": "请输入被告的真实姓名", "validation": "^[\\u4e00-\\u9fa5]{2,10}$"}, "defendant_gender": {"label": "被告性别", "type": "select", "required": true, "options": ["男", "女"]}, "defendant_birth": {"label": "被告出生日期", "type": "date", "required": true, "placeholder": "YYYY-MM-DD"}, "defendant_nation": {"label": "被告民族", "type": "text", "required": true, "placeholder": "如：汉族", "default": "汉族"}, "defendant_address": {"label": "被告住址", "type": "textarea", "required": true, "placeholder": "请输入详细住址"}, "defendant_phone": {"label": "被告联系电话", "type": "tel", "required": false, "placeholder": "请输入手机号码（如知道）", "validation": "^1[3-9]\\d{9}$"}, "defendant_legal_rep": {"label": "被告法定代表人", "type": "text", "required": false, "placeholder": "如被告为企业，请填写法定代表人姓名"}, "husband_name": {"label": "男方姓名", "type": "text", "required": true, "placeholder": "请输入男方真实姓名", "validation": "^[\\u4e00-\\u9fa5]{2,10}$"}, "husband_id": {"label": "男方身份证号", "type": "text", "required": true, "placeholder": "请输入18位身份证号码", "validation": "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$"}, "husband_address": {"label": "男方住址", "type": "textarea", "required": true, "placeholder": "请输入详细住址"}, "wife_name": {"label": "女方姓名", "type": "text", "required": true, "placeholder": "请输入女方真实姓名", "validation": "^[\\u4e00-\\u9fa5]{2,10}$"}, "wife_id": {"label": "女方身份证号", "type": "text", "required": true, "placeholder": "请输入18位身份证号码", "validation": "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$"}, "wife_address": {"label": "女方住址", "type": "textarea", "required": true, "placeholder": "请输入详细住址"}, "marriage_date": {"label": "结婚日期", "type": "date", "required": true, "placeholder": "YYYY-MM-DD"}, "marriage_place": {"label": "结婚登记地", "type": "text", "required": true, "placeholder": "如：XX市XX区民政局"}, "children_info": {"label": "子女情况", "type": "textarea", "required": false, "placeholder": "请详细描述子女姓名、年龄、目前跟随情况等"}, "property_info": {"label": "财产情况", "type": "textarea", "required": false, "placeholder": "请详细描述夫妻共同财产情况"}, "property_division": {"label": "财产分割方案", "type": "textarea", "required": true, "placeholder": "请详细描述财产分割的具体方案"}, "debt_division": {"label": "债务承担方案", "type": "textarea", "required": false, "placeholder": "请详细描述债务承担的具体方案"}, "child_custody": {"label": "子女抚养权", "type": "textarea", "required": false, "placeholder": "请详细描述子女抚养权归属"}, "child_support": {"label": "抚养费标准", "type": "textarea", "required": false, "placeholder": "请详细描述抚养费支付标准和方式"}, "visitation_rights": {"label": "探视权安排", "type": "textarea", "required": false, "placeholder": "请详细描述探视权的具体安排"}, "divorce_reason": {"label": "离婚原因", "type": "textarea", "required": true, "placeholder": "请详细描述导致夫妻感情破裂的具体原因"}, "court_name": {"label": "受理法院", "type": "text", "required": true, "placeholder": "如：XX市XX区人民法院"}, "applicant_name": {"label": "申请人姓名", "type": "text", "required": true, "placeholder": "请输入申请人真实姓名", "validation": "^[\\u4e00-\\u9fa5]{2,10}$"}, "respondent_name": {"label": "被申请人名称", "type": "text", "required": true, "placeholder": "请输入用人单位全称"}, "respondent_address": {"label": "被申请人地址", "type": "textarea", "required": true, "placeholder": "请输入用人单位详细地址"}, "work_start_date": {"label": "入职日期", "type": "date", "required": true, "placeholder": "YYYY-MM-DD"}, "work_end_date": {"label": "离职日期", "type": "date", "required": false, "placeholder": "YYYY-MM-DD（如仍在职可不填）"}, "position": {"label": "工作岗位", "type": "text", "required": true, "placeholder": "请输入具体工作岗位"}, "salary": {"label": "工资标准", "type": "text", "required": true, "placeholder": "请输入月工资或小时工资标准"}, "dispute_facts": {"label": "争议事实", "type": "textarea", "required": true, "placeholder": "请详细描述劳动争议的具体事实"}, "arbitration_requests": {"label": "仲裁请求", "type": "textarea", "required": true, "placeholder": "请详细列出具体的仲裁请求"}, "evidence_list": {"label": "证据清单", "type": "textarea", "required": false, "placeholder": "请列出支持仲裁请求的证据材料"}, "arbitration_committee": {"label": "仲裁委员会", "type": "text", "required": true, "placeholder": "如：XX市劳动人事争议仲裁委员会"}, "case_facts": {"label": "案件事实", "type": "textarea", "required": true, "placeholder": "请详细描述案件的基本事实"}, "legal_basis": {"label": "法律依据", "type": "textarea", "required": true, "placeholder": "请列出相关的法律条文依据"}, "litigation_requests": {"label": "诉讼请求", "type": "textarea", "required": true, "placeholder": "请详细列出具体的诉讼请求"}, "recipient_name": {"label": "收件人姓名", "type": "text", "required": true, "placeholder": "请输入收件人姓名或单位名称"}, "recipient_address": {"label": "收件人地址", "type": "textarea", "required": true, "placeholder": "请输入收件人详细地址"}, "client_name": {"label": "委托人姓名", "type": "text", "required": true, "placeholder": "请输入委托人真实姓名"}, "lawyer_name": {"label": "律师姓名", "type": "text", "required": true, "placeholder": "请输入律师姓名"}, "law_firm": {"label": "律师事务所", "type": "text", "required": true, "placeholder": "请输入律师事务所全称"}, "case_description": {"label": "案件描述", "type": "textarea", "required": true, "placeholder": "请详细描述案件情况"}, "legal_analysis": {"label": "法律分析", "type": "textarea", "required": true, "placeholder": "请提供相关的法律分析"}, "demands": {"label": "具体要求", "type": "textarea", "required": true, "placeholder": "请列出具体的要求和主张"}, "deadline": {"label": "回复期限", "type": "text", "required": true, "placeholder": "如：收到本函后7日内"}, "consequences": {"label": "法律后果", "type": "textarea", "required": true, "placeholder": "请说明不履行的法律后果"}}}