from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import sys
import logging
import json
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.scenario_agent import ScenarioAgent
from agents.conversation_agent import ConversationAgent
from agents.vocab_agent import VocabAgent
from agents.document_agent import DocumentAgent
from utils.logger import LOG

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 配置日志
logging.basicConfig(level=logging.INFO)

# 全局智能体实例
agents = {
    'scenario': {},  # 场景代理将根据场景类型动态创建
    'conversation': ConversationAgent(),
    'vocab': VocabAgent(),
    'document': DocumentAgent()
}

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'message': 'LegalConsultationAssistant API is running'
    })

@app.route('/api/scenario/list', methods=['GET'])
def get_scenarios():
    """获取可用场景列表"""
    scenarios = [
        {
            'id': 'marriage_dispute',
            'name': '婚姻纠纷',
            'description': '离婚、财产分割、子女抚养等婚姻法律问题',
            'icon': 'fas fa-heart-broken'
        },
        {
            'id': 'contract_dispute',
            'name': '合同纠纷',
            'description': '合同效力、违约责任、争议解决等问题',
            'icon': 'fas fa-file-contract'
        },
        {
            'id': 'work_injury',
            'name': '工伤赔偿',
            'description': '工伤认定、伤残鉴定、赔偿计算等问题',
            'icon': 'fas fa-hard-hat'
        }
    ]
    return jsonify({
        'success': True,
        'data': scenarios
    })

@app.route('/api/scenario/<scenario_id>/intro', methods=['GET'])
def get_scenario_intro(scenario_id):
    """获取场景介绍"""
    try:
        # 获取项目根目录路径
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        intro_file = os.path.join(project_root, "content", "page", f"{scenario_id}.md")

        if os.path.exists(intro_file):
            with open(intro_file, 'r', encoding='utf-8') as f:
                intro_content = f.read()
            return jsonify({
                'success': True,
                'data': {
                    'intro': intro_content
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': f'场景介绍文件未找到: {scenario_id}'
            }), 404
    except Exception as e:
        LOG.error(f"获取场景介绍失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/scenario/<scenario_id>/start', methods=['POST'])
def start_scenario(scenario_id):
    """开始场景对话"""
    try:
        data = request.get_json()
        session_id = data.get('session_id', f"{scenario_id}_default")
        
        # 创建或获取场景代理
        if scenario_id not in agents['scenario']:
            agents['scenario'][scenario_id] = ScenarioAgent(scenario_id, session_id)
        
        agent = agents['scenario'][scenario_id]
        initial_message = agent.start_new_session(session_id)
        
        return jsonify({
            'success': True,
            'data': {
                'message': initial_message,
                'session_id': session_id
            }
        })
    except Exception as e:
        LOG.error(f"开始场景对话失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/scenario/<scenario_id>/chat', methods=['POST'])
def scenario_chat(scenario_id):
    """场景对话"""
    try:
        data = request.get_json()
        user_input = data.get('message', '')
        session_id = data.get('session_id', f"{scenario_id}_default")
        
        if not user_input:
            return jsonify({
                'success': False,
                'error': '消息不能为空'
            }), 400
        
        # 获取或创建场景代理
        if scenario_id not in agents['scenario']:
            agents['scenario'][scenario_id] = ScenarioAgent(scenario_id, session_id)
        
        agent = agents['scenario'][scenario_id]
        bot_response = agent.chat_with_history(user_input, session_id)
        
        return jsonify({
            'success': True,
            'data': {
                'message': bot_response
            }
        })
    except Exception as e:
        LOG.error(f"场景对话失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/conversation/chat', methods=['POST'])
def conversation_chat():
    """自由对话"""
    try:
        data = request.get_json()
        user_input = data.get('message', '')
        session_id = data.get('session_id', 'conversation_default')
        
        if not user_input:
            return jsonify({
                'success': False,
                'error': '消息不能为空'
            }), 400
        
        agent = agents['conversation']
        bot_response = agent.chat_with_history(user_input, session_id)
        
        return jsonify({
            'success': True,
            'data': {
                'message': bot_response
            }
        })
    except Exception as e:
        LOG.error(f"自由对话失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/vocab/intro', methods=['GET'])
def get_vocab_intro():
    """获取词汇学习介绍"""
    try:
        # 获取项目根目录路径
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        intro_file = os.path.join(project_root, "content", "page", "vocab_study.md")

        if os.path.exists(intro_file):
            with open(intro_file, 'r', encoding='utf-8') as f:
                intro_content = f.read()
            return jsonify({
                'success': True,
                'data': {
                    'intro': intro_content
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': '词汇学习介绍文件未找到'
            }), 404
    except Exception as e:
        LOG.error(f"获取词汇学习介绍失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/vocab/start', methods=['POST'])
def start_vocab():
    """开始法律学习"""
    try:
        data = request.get_json()
        session_id = data.get('session_id', 'vocab_default')
        book_type = data.get('book_type', None)  # 新增书籍类型参数

        agent = agents['vocab']
        agent.restart_session(session_id)

        # 如果指定了书籍类型，设置书籍记忆
        if book_type:
            agent.set_book_memory(book_type, session_id)

        # 发送初始消息
        initial_input = "开始学习"
        bot_response = agent.chat_with_history(initial_input, session_id)

        return jsonify({
            'success': True,
            'data': {
                'user_message': initial_input,
                'bot_message': bot_response,
                'session_id': session_id,
                'book_type': book_type
            }
        })
    except Exception as e:
        LOG.error(f"开始法律学习失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/vocab/chat', methods=['POST'])
def vocab_chat():
    """法律学习对话"""
    try:
        data = request.get_json()
        user_input = data.get('message', '')
        session_id = data.get('session_id', 'vocab_default')

        if not user_input:
            return jsonify({
                'success': False,
                'error': '消息不能为空'
            }), 400

        agent = agents['vocab']
        bot_response = agent.chat_with_history(user_input, session_id)

        # 获取当前书籍记忆信息
        book_info = agent.get_book_memory(session_id)

        return jsonify({
            'success': True,
            'data': {
                'message': bot_response,
                'book_info': book_info
            }
        })
    except Exception as e:
        LOG.error(f"法律学习对话失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/vocab/reset_book', methods=['POST'])
def reset_book_memory():
    """重置书籍记忆"""
    try:
        data = request.get_json()
        session_id = data.get('session_id', 'vocab_default')

        agent = agents['vocab']
        agent.restart_session(session_id)  # 这会清除书籍记忆和会话历史

        return jsonify({
            'success': True,
            'data': {
                'message': '书籍记忆已重置'
            }
        })
    except Exception as e:
        LOG.error(f"重置书籍记忆失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/config', methods=['POST'])
def update_config():
    """更新配置（API Key等）"""
    try:
        data = request.get_json()
        api_key = data.get('api_key')
        api_base = data.get('api_base')
        
        if api_key:
            os.environ['OPENAI_API_KEY'] = api_key
        if api_base:
            os.environ['OPENAI_API_BASE'] = api_base
        
        # 重新初始化智能体以使用新配置
        agents['conversation'] = ConversationAgent()
        agents['vocab'] = VocabAgent()
        agents['document'] = DocumentAgent()
        agents['scenario'] = {}  # 清空场景代理，将在下次使用时重新创建

        return jsonify({
            'success': True,
            'message': '配置更新成功'
        })
    except Exception as e:
        LOG.error(f"更新配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/document/types', methods=['GET'])
def get_document_types():
    """获取可用的文书类型列表"""
    try:
        agent = agents['document']
        document_types = agent.get_document_types()

        return jsonify({
            'success': True,
            'data': document_types
        })
    except Exception as e:
        LOG.error(f"获取文书类型失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/document/<document_type>/fields', methods=['GET'])
def get_document_fields(document_type):
    """获取指定文书类型的字段信息"""
    try:
        agent = agents['document']
        fields_info = agent.get_document_fields(document_type)

        if fields_info is None:
            return jsonify({
                'success': False,
                'error': f'不支持的文书类型: {document_type}'
            }), 404

        # 加载字段定义
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        field_definitions_file = os.path.join(project_root, "content", "document_templates", "field_definitions.json")

        if os.path.exists(field_definitions_file):
            with open(field_definitions_file, 'r', encoding='utf-8') as f:
                field_definitions = json.load(f)['field_definitions']
        else:
            field_definitions = {}

        # 加载文书类型配置
        document_types_file = os.path.join(project_root, "content", "document_templates", "document_types.json")
        document_config = None

        if os.path.exists(document_types_file):
            with open(document_types_file, 'r', encoding='utf-8') as f:
                document_types = json.load(f)['document_types']
                for doc_type in document_types:
                    if doc_type['id'] == document_type:
                        document_config = doc_type
                        break

        # 构建字段信息
        fields = []
        if document_config and 'field_groups' in document_config:
            for group in document_config['field_groups']:
                group_fields = []
                for field_name in group['fields']:
                    if field_name in field_definitions:
                        field_info = field_definitions[field_name].copy()
                        field_info['name'] = field_name
                        group_fields.append(field_info)

                fields.append({
                    'title': group['title'],
                    'fields': group_fields
                })

        return jsonify({
            'success': True,
            'data': {
                'document_info': fields_info,
                'document_config': document_config,
                'field_groups': fields
            }
        })
    except Exception as e:
        LOG.error(f"获取文书字段失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/document/<document_type>/generate', methods=['POST'])
def generate_document(document_type):
    """生成法律文书"""
    try:
        data = request.get_json()
        user_data = data.get('user_data', {})
        session_id = data.get('session_id', f"document_{document_type}_{int(time.time())}")

        agent = agents['document']

        # 验证必填字段
        is_valid, validation_message = agent.validate_required_fields(document_type, user_data)
        if not is_valid:
            return jsonify({
                'success': False,
                'error': validation_message
            }), 400

        # 生成文书
        result = agent.generate_document(document_type, user_data, session_id)

        return jsonify(result)

    except Exception as e:
        LOG.error(f"生成文书失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/document/<document_type>/validate', methods=['POST'])
def validate_document_data(document_type):
    """验证文书数据的完整性"""
    try:
        data = request.get_json()
        user_data = data.get('user_data', {})

        agent = agents['document']
        is_valid, validation_message = agent.validate_required_fields(document_type, user_data)

        return jsonify({
            'success': True,
            'data': {
                'is_valid': is_valid,
                'message': validation_message
            }
        })

    except Exception as e:
        LOG.error(f"验证文书数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
