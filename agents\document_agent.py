import json
import os
from datetime import datetime
from langchain_core.messages import AIMessage, HumanMessage

from .session_history import get_session_history
from .agent_base import AgentBase
from utils.logger import LOG


class DocumentAgent(AgentBase):
    """
    文书生成代理类，负责处理法律文书的智能生成。
    """
    
    def __init__(self, session_id=None):
        super().__init__(
            name="document_generation",
            prompt_file="prompts/document_generation_prompt.txt",
            session_id=session_id
        )
        
        # 文书模板配置
        self.document_templates = self._load_document_templates()
        
    def _load_document_templates(self):
        """加载文书模板配置"""
        return {
            "divorce_petition": {
                "name": "离婚起诉状",
                "category": "婚姻家庭",
                "description": "用于向法院申请离婚的法律文书",
                "required_fields": [
                    "plaintiff_name", "plaintiff_gender", "plaintiff_birth", 
                    "plaintiff_nation", "plaintiff_address", "plaintiff_phone",
                    "defendant_name", "defendant_gender", "defendant_birth",
                    "defendant_nation", "defendant_address", "defendant_phone",
                    "marriage_date", "marriage_place", "children_info",
                    "property_info", "divorce_reason", "court_name"
                ]
            },
            "divorce_agreement": {
                "name": "离婚协议书",
                "category": "婚姻家庭", 
                "description": "夫妻双方协议离婚时签署的协议书",
                "required_fields": [
                    "husband_name", "husband_id", "husband_address",
                    "wife_name", "wife_id", "wife_address",
                    "marriage_date", "marriage_place", "children_info",
                    "property_division", "debt_division", "child_custody",
                    "child_support", "visitation_rights"
                ]
            },
            "labor_arbitration": {
                "name": "劳动仲裁申请书",
                "category": "劳动争议",
                "description": "向劳动仲裁委员会申请仲裁的法律文书",
                "required_fields": [
                    "applicant_name", "applicant_gender", "applicant_birth",
                    "applicant_address", "applicant_phone", "applicant_id",
                    "respondent_name", "respondent_address", "respondent_legal_rep",
                    "work_start_date", "work_end_date", "position", "salary",
                    "dispute_facts", "arbitration_requests", "evidence_list",
                    "arbitration_committee"
                ]
            },
            "civil_complaint": {
                "name": "民事起诉状",
                "category": "民事诉讼",
                "description": "向法院提起民事诉讼的法律文书",
                "required_fields": [
                    "plaintiff_name", "plaintiff_gender", "plaintiff_birth",
                    "plaintiff_address", "plaintiff_phone", "plaintiff_id",
                    "defendant_name", "defendant_address", "defendant_legal_rep",
                    "case_facts", "legal_basis", "litigation_requests",
                    "evidence_list", "court_name"
                ]
            },
            "lawyer_letter": {
                "name": "律师函",
                "category": "其他文书",
                "description": "律师代表委托人发出的法律文书",
                "required_fields": [
                    "recipient_name", "recipient_address", "client_name",
                    "lawyer_name", "law_firm", "case_description",
                    "legal_analysis", "demands", "deadline", "consequences"
                ]
            }
        }
    
    def get_document_types(self):
        """获取所有可用的文书类型"""
        document_types = []
        for doc_id, doc_info in self.document_templates.items():
            document_types.append({
                "id": doc_id,
                "name": doc_info["name"],
                "category": doc_info["category"],
                "description": doc_info["description"]
            })
        return document_types
    
    def get_document_fields(self, document_type):
        """获取指定文书类型的必填字段"""
        if document_type not in self.document_templates:
            return None
        
        template = self.document_templates[document_type]
        return {
            "name": template["name"],
            "category": template["category"],
            "description": template["description"],
            "required_fields": template["required_fields"]
        }
    
    def generate_document(self, document_type, user_data, session_id=None):
        """
        生成法律文书
        
        参数:
            document_type (str): 文书类型
            user_data (dict): 用户提供的数据
            session_id (str, optional): 会话ID
            
        返回:
            dict: 包含生成的文书内容和相关信息
        """
        if session_id is None:
            session_id = self.session_id
            
        if document_type not in self.document_templates:
            return {
                "success": False,
                "error": f"不支持的文书类型: {document_type}"
            }
        
        template = self.document_templates[document_type]
        
        # 构建生成文书的提示
        prompt = self._build_generation_prompt(document_type, template, user_data)
        
        try:
            # 使用AI生成文书
            generated_content = self.chat_with_history(prompt, session_id)
            
            return {
                "success": True,
                "document_type": document_type,
                "document_name": template["name"],
                "content": generated_content,
                "generated_at": datetime.now().isoformat(),
                "user_data": user_data
            }
            
        except Exception as e:
            LOG.error(f"文书生成失败: {str(e)}")
            return {
                "success": False,
                "error": f"文书生成失败: {str(e)}"
            }
    
    def _build_generation_prompt(self, document_type, template, user_data):
        """构建文书生成的提示词"""
        prompt = f"""请根据以下信息生成一份标准的{template['name']}：

文书类型：{template['name']}
文书类别：{template['category']}

用户提供的信息：
"""
        
        for field, value in user_data.items():
            if value:  # 只包含有值的字段
                prompt += f"- {field}: {value}\n"
        
        prompt += f"""

请严格按照{template['name']}的标准格式生成文书，确保：
1. 格式规范，符合法律文书要求
2. 内容完整，包含所有必要要素
3. 用词准确，使用规范的法律术语
4. 逻辑清晰，条理分明
5. 在文书末尾添加使用说明和法律风险提示

请直接生成完整的文书内容。"""

        return prompt
    
    def validate_required_fields(self, document_type, user_data):
        """验证必填字段是否完整"""
        if document_type not in self.document_templates:
            return False, "不支持的文书类型"
        
        template = self.document_templates[document_type]
        required_fields = template["required_fields"]
        missing_fields = []
        
        for field in required_fields:
            if field not in user_data or not user_data[field]:
                missing_fields.append(field)
        
        if missing_fields:
            return False, f"缺少必填字段: {', '.join(missing_fields)}"
        
        return True, "验证通过"
