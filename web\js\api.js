// API 基础配置
const API_BASE_URL = 'http://localhost:5000/api';

// API 调用工具类
class LanguageMentorAPI {
    constructor() {
        this.baseURL = API_BASE_URL;
    }

    // 通用请求方法
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || `HTTP error! status: ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    // GET 请求
    async get(endpoint) {
        return this.request(endpoint, { method: 'GET' });
    }

    // POST 请求
    async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // 健康检查
    async healthCheck() {
        return this.get('/health');
    }

    // 场景相关 API
    async getScenarios() {
        return this.get('/scenario/list');
    }

    async getScenarioIntro(scenarioId) {
        return this.get(`/scenario/${scenarioId}/intro`);
    }

    async startScenario(scenarioId, sessionId = null) {
        return this.post(`/scenario/${scenarioId}/start`, {
            session_id: sessionId || `${scenarioId}_${Date.now()}`
        });
    }

    async scenarioChat(scenarioId, message, sessionId) {
        return this.post(`/scenario/${scenarioId}/chat`, {
            message,
            session_id: sessionId
        });
    }

    // 自由对话 API
    async conversationChat(message, sessionId = null) {
        return this.post('/conversation/chat', {
            message,
            session_id: sessionId || `conversation_${Date.now()}`
        });
    }

    // 词汇学习 API
    async getVocabIntro() {
        return this.get('/vocab/intro');
    }

    async startVocab(sessionId = null, bookType = null) {
        return this.post('/vocab/start', {
            session_id: sessionId || `vocab_${Date.now()}`,
            book_type: bookType
        });
    }

    async vocabChat(message, sessionId) {
        return this.post('/vocab/chat', {
            message,
            session_id: sessionId
        });
    }

    // 重置书籍记忆 API
    async resetBookMemory(sessionId) {
        return this.post('/vocab/reset_book', {
            session_id: sessionId
        });
    }

    // 配置更新 API
    async updateConfig(apiKey) {
        return this.post('/config', {
            api_key: apiKey
        });
    }
}

// 创建全局 API 实例
const api = new LanguageMentorAPI();

// 会话管理
class SessionManager {
    constructor() {
        this.sessions = {
            scenario: {},
            conversation: null,
            vocab: null
        };
    }

    // 获取或创建会话ID
    getSessionId(type, subType = null) {
        if (type === 'scenario' && subType) {
            if (!this.sessions.scenario[subType]) {
                this.sessions.scenario[subType] = `${subType}_${Date.now()}`;
            }
            return this.sessions.scenario[subType];
        } else if (type === 'conversation') {
            if (!this.sessions.conversation) {
                this.sessions.conversation = `conversation_${Date.now()}`;
            }
            return this.sessions.conversation;
        } else if (type === 'vocab') {
            if (!this.sessions.vocab) {
                this.sessions.vocab = `vocab_${Date.now()}`;
            }
            return this.sessions.vocab;
        }
        return null;
    }

    // 重置会话
    resetSession(type, subType = null) {
        if (type === 'scenario' && subType) {
            this.sessions.scenario[subType] = `${subType}_${Date.now()}`;
        } else if (type === 'conversation') {
            this.sessions.conversation = `conversation_${Date.now()}`;
        } else if (type === 'vocab') {
            this.sessions.vocab = `vocab_${Date.now()}`;
        }
    }

    // 清除所有会话
    clearAllSessions() {
        this.sessions = {
            scenario: {},
            conversation: null,
            vocab: null
        };
    }
}

// 创建全局会话管理器实例
const sessionManager = new SessionManager();

// 错误处理工具
class ErrorHandler {
    static show(message, type = 'error') {
        // 创建错误提示元素
        const errorDiv = document.createElement('div');
        errorDiv.className = `error-toast ${type}`;
        errorDiv.innerHTML = `
            <div class="error-content">
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
                <button class="error-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // 添加样式
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#fee' : '#eff6ff'};
            color: ${type === 'error' ? '#c53030' : '#2563eb'};
            border: 1px solid ${type === 'error' ? '#fed7d7' : '#dbeafe'};
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1001;
            max-width: 400px;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(errorDiv);

        // 自动移除
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 5000);
    }

    static showSuccess(message) {
        this.show(message, 'success');
    }

    static showInfo(message) {
        this.show(message, 'info');
    }
}

// 加载状态管理
class LoadingManager {
    static show(message = '正在处理...') {
        const overlay = document.getElementById('loadingOverlay');
        const text = overlay.querySelector('p');
        if (text) {
            text.textContent = message;
        }
        overlay.classList.add('show');
    }

    static hide() {
        const overlay = document.getElementById('loadingOverlay');
        overlay.classList.remove('show');
    }
}

// 添加错误提示样式到页面
const errorStyles = document.createElement('style');
errorStyles.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .error-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .error-close {
        background: none;
        border: none;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 4px;
        margin-left: auto;
    }
    
    .error-close:hover {
        background: rgba(0, 0, 0, 0.1);
    }
`;
document.head.appendChild(errorStyles);
