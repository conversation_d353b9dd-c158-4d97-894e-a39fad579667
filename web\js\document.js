// 文书生成功能模块
class DocumentGenerator {
    constructor() {
        this.api = new LanguageMentorAPI();
        this.sessionManager = new SessionManager();
        this.currentDocumentType = null;
        this.currentFieldGroups = null;
        this.formData = {};
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadDocumentTypes();
    }
    
    bindEvents() {
        // 返回选择按钮
        document.getElementById('backToSelector')?.addEventListener('click', () => {
            this.showDocumentSelector();
        });
        
        // 验证表单按钮
        document.getElementById('validateForm')?.addEventListener('click', () => {
            this.validateForm();
        });
        
        // 生成文书按钮
        document.getElementById('generateDocument')?.addEventListener('click', () => {
            this.generateDocument();
        });
        
        // 编辑文书按钮
        document.getElementById('editDocument')?.addEventListener('click', () => {
            this.showDocumentForm();
        });
        
        // 复制文本按钮
        document.getElementById('copyDocument')?.addEventListener('click', () => {
            this.copyDocumentText();
        });
        
        // 下载文档按钮
        document.getElementById('downloadDocument')?.addEventListener('click', () => {
            this.downloadDocument();
        });
    }
    
    async loadDocumentTypes() {
        try {
            showLoading(true);
            const response = await this.api.getDocumentTypes();
            
            if (response.success) {
                this.renderDocumentTypes(response.data);
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('加载文书类型失败:', error);
            this.showError('加载文书类型失败，请刷新页面重试');
        } finally {
            showLoading(false);
        }
    }
    
    renderDocumentTypes(documentTypes) {
        const container = document.getElementById('documentTypes');
        if (!container) return;
        
        container.innerHTML = documentTypes.map(docType => `
            <div class="document-type-card" data-type="${docType.id}">
                <div class="document-type-header">
                    <i class="${docType.icon} document-type-icon"></i>
                    <div>
                        <h4 class="document-type-title">${docType.name}</h4>
                        <div class="document-type-category">${docType.category}</div>
                    </div>
                </div>
                <p class="document-type-description">${docType.description}</p>
                <div class="document-type-meta">
                    <span class="complexity-badge complexity-${this.getComplexityClass(docType.complexity || '中等')}">
                        ${docType.complexity || '中等'}
                    </span>
                    <span class="estimated-time">
                        <i class="fas fa-clock"></i>
                        ${docType.estimated_time || '15-20分钟'}
                    </span>
                </div>
            </div>
        `).join('');
        
        // 绑定点击事件
        container.querySelectorAll('.document-type-card').forEach(card => {
            card.addEventListener('click', () => {
                this.selectDocumentType(card.dataset.type);
            });
        });
    }
    
    getComplexityClass(complexity) {
        const complexityMap = {
            '简单': 'simple',
            '中等': 'medium', 
            '复杂': 'complex'
        };
        return complexityMap[complexity] || 'medium';
    }
    
    async selectDocumentType(documentType) {
        try {
            showLoading(true);
            
            // 更新选中状态
            document.querySelectorAll('.document-type-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-type="${documentType}"]`)?.classList.add('selected');
            
            // 加载字段信息
            const response = await this.api.getDocumentFields(documentType);
            
            if (response.success) {
                this.currentDocumentType = documentType;
                this.currentFieldGroups = response.data.field_groups;
                this.showDocumentForm(response.data);
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('加载文书字段失败:', error);
            this.showError('加载文书字段失败，请重试');
        } finally {
            showLoading(false);
        }
    }
    
    showDocumentForm(data) {
        // 隐藏选择器，显示表单
        document.getElementById('documentSelector').style.display = 'none';
        document.getElementById('documentResultContainer').style.display = 'none';
        document.getElementById('documentFormContainer').style.display = 'block';
        
        if (data) {
            // 更新表单标题和描述
            document.getElementById('selectedDocumentTitle').textContent = data.document_info.name;
            document.getElementById('selectedDocumentDescription').textContent = data.document_info.description;
            
            // 生成表单
            this.renderForm(data.field_groups);
        }
        
        // 重置进度
        this.updateProgress();
    }
    
    renderForm(fieldGroups) {
        const form = document.getElementById('documentForm');
        if (!form) return;
        
        form.innerHTML = fieldGroups.map(group => `
            <div class="form-group">
                <h4 class="form-group-title">
                    <i class="fas fa-folder"></i>
                    ${group.title}
                </h4>
                <div class="form-fields">
                    ${group.fields.map(field => this.renderField(field)).join('')}
                </div>
            </div>
        `).join('');
        
        // 绑定输入事件
        form.querySelectorAll('.field-input').forEach(input => {
            input.addEventListener('input', () => {
                this.updateFormData();
                this.updateProgress();
                this.clearFieldError(input);
            });
            
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
        });
    }
    
    renderField(field) {
        const isRequired = field.required ? 'required' : '';
        const requiredClass = field.required ? 'required' : '';
        
        let inputHtml = '';
        
        switch (field.type) {
            case 'select':
                inputHtml = `
                    <select class="field-input" name="${field.name}" ${isRequired}>
                        <option value="">请选择...</option>
                        ${field.options.map(option => `<option value="${option}">${option}</option>`).join('')}
                    </select>
                `;
                break;
            case 'textarea':
                inputHtml = `
                    <textarea class="field-input" name="${field.name}" 
                              placeholder="${field.placeholder || ''}" 
                              rows="3" ${isRequired}></textarea>
                `;
                break;
            case 'date':
                inputHtml = `
                    <input type="date" class="field-input" name="${field.name}" ${isRequired}>
                `;
                break;
            case 'tel':
                inputHtml = `
                    <input type="tel" class="field-input" name="${field.name}" 
                           placeholder="${field.placeholder || ''}" ${isRequired}>
                `;
                break;
            default:
                inputHtml = `
                    <input type="text" class="field-input" name="${field.name}" 
                           placeholder="${field.placeholder || ''}" ${isRequired}>
                `;
        }
        
        return `
            <div class="field-container">
                <label class="field-label ${requiredClass}">${field.label}</label>
                ${inputHtml}
                <div class="field-error" id="error-${field.name}"></div>
            </div>
        `;
    }
    
    updateFormData() {
        const form = document.getElementById('documentForm');
        if (!form) return;
        
        this.formData = {};
        const formData = new FormData(form);
        
        for (let [key, value] of formData.entries()) {
            this.formData[key] = value.trim();
        }
    }
    
    updateProgress() {
        if (!this.currentFieldGroups) return;
        
        // 计算所有必填字段
        const allRequiredFields = [];
        this.currentFieldGroups.forEach(group => {
            group.fields.forEach(field => {
                if (field.required) {
                    allRequiredFields.push(field.name);
                }
            });
        });
        
        // 计算已填写的必填字段
        const filledRequiredFields = allRequiredFields.filter(fieldName => {
            return this.formData[fieldName] && this.formData[fieldName].length > 0;
        });
        
        const progress = allRequiredFields.length > 0 
            ? Math.round((filledRequiredFields.length / allRequiredFields.length) * 100)
            : 0;
        
        // 更新进度条
        const progressFill = document.getElementById('formProgress');
        const progressText = document.getElementById('progressText');
        
        if (progressFill) progressFill.style.width = `${progress}%`;
        if (progressText) progressText.textContent = `${progress}%`;
        
        // 更新生成按钮状态
        const generateBtn = document.getElementById('generateDocument');
        if (generateBtn) {
            generateBtn.disabled = progress < 100;
        }
    }
    
    validateField(input) {
        const field = this.getFieldConfig(input.name);
        if (!field) return true;
        
        let isValid = true;
        let errorMessage = '';
        
        // 检查必填字段
        if (field.required && !input.value.trim()) {
            isValid = false;
            errorMessage = `${field.label}是必填项`;
        }
        
        // 检查正则验证
        if (isValid && field.validation && input.value.trim()) {
            const regex = new RegExp(field.validation);
            if (!regex.test(input.value.trim())) {
                isValid = false;
                errorMessage = `${field.label}格式不正确`;
            }
        }
        
        // 显示错误信息
        if (!isValid) {
            this.showFieldError(input.name, errorMessage);
            input.classList.add('error');
        } else {
            this.clearFieldError(input);
            input.classList.remove('error');
        }
        
        return isValid;
    }
    
    getFieldConfig(fieldName) {
        if (!this.currentFieldGroups) return null;
        
        for (let group of this.currentFieldGroups) {
            for (let field of group.fields) {
                if (field.name === fieldName) {
                    return field;
                }
            }
        }
        return null;
    }
    
    showFieldError(fieldName, message) {
        const errorElement = document.getElementById(`error-${fieldName}`);
        if (errorElement) {
            errorElement.textContent = message;
        }
    }
    
    clearFieldError(input) {
        const errorElement = document.getElementById(`error-${input.name}`);
        if (errorElement) {
            errorElement.textContent = '';
        }
    }
    
    async validateForm() {
        try {
            showLoading(true);
            this.updateFormData();
            
            // 前端验证
            const form = document.getElementById('documentForm');
            const inputs = form.querySelectorAll('.field-input');
            let isValid = true;
            
            inputs.forEach(input => {
                if (!this.validateField(input)) {
                    isValid = false;
                }
            });
            
            if (!isValid) {
                this.showError('请修正表单中的错误信息');
                return;
            }
            
            // 后端验证
            const response = await this.api.validateDocumentData(this.currentDocumentType, this.formData);
            
            if (response.success) {
                if (response.data.is_valid) {
                    this.showSuccess('信息验证通过，可以生成文书');
                    document.getElementById('generateDocument').disabled = false;
                } else {
                    this.showError(response.data.message);
                }
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('验证失败:', error);
            this.showError('验证失败，请重试');
        } finally {
            showLoading(false);
        }
    }

    async generateDocument() {
        try {
            showLoading(true);
            this.updateFormData();

            const sessionId = this.sessionManager.getSessionId('document', this.currentDocumentType);
            const response = await this.api.generateDocument(this.currentDocumentType, this.formData, sessionId);

            if (response.success) {
                this.showDocumentResult(response);
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('生成文书失败:', error);
            this.showError('生成文书失败，请重试');
        } finally {
            showLoading(false);
        }
    }

    showDocumentResult(result) {
        // 隐藏表单，显示结果
        document.getElementById('documentFormContainer').style.display = 'none';
        document.getElementById('documentResultContainer').style.display = 'block';

        // 显示生成的文书内容
        const preview = document.getElementById('documentPreview');
        if (preview) {
            // 添加预览工具栏
            const toolbar = this.createPreviewToolbar();
            const contentDiv = document.createElement('div');
            contentDiv.className = 'preview-content';
            contentDiv.style.fontSize = '16px';
            contentDiv.style.lineHeight = '1.8';
            contentDiv.textContent = result.content;

            preview.innerHTML = '';
            preview.appendChild(toolbar);
            preview.appendChild(contentDiv);
        }

        // 保存结果数据
        this.currentResult = result;
    }

    createPreviewToolbar() {
        const toolbar = document.createElement('div');
        toolbar.className = 'preview-toolbar';

        toolbar.innerHTML = `
            <div class="preview-controls">
                <div class="zoom-control">
                    <span>字体大小:</span>
                    <button class="zoom-btn" onclick="documentGenerator.adjustFontSize(-2)">
                        <i class="fas fa-minus"></i>
                    </button>
                    <span id="currentFontSize">16px</span>
                    <button class="zoom-btn" onclick="documentGenerator.adjustFontSize(2)">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="preview-actions">
                <button class="print-btn" onclick="documentGenerator.printDocument()">
                    <i class="fas fa-print"></i>
                    打印预览
                </button>
            </div>
        `;

        return toolbar;
    }

    adjustFontSize(delta) {
        const contentDiv = document.querySelector('.preview-content');
        const currentSizeSpan = document.getElementById('currentFontSize');

        if (contentDiv && currentSizeSpan) {
            const currentSize = parseInt(contentDiv.style.fontSize) || 16;
            const newSize = Math.max(12, Math.min(24, currentSize + delta));

            contentDiv.style.fontSize = `${newSize}px`;
            currentSizeSpan.textContent = `${newSize}px`;
        }
    }

    printDocument() {
        if (!this.currentResult) return;

        // 创建打印窗口
        const printWindow = window.open('', '_blank');
        const content = this.formatContentForHtml(this.currentResult.content);

        printWindow.document.write(content);
        printWindow.document.close();

        // 等待内容加载完成后打印
        printWindow.onload = function() {
            printWindow.print();
            printWindow.close();
        };
    }

    showDocumentSelector() {
        // 显示选择器，隐藏其他
        document.getElementById('documentSelector').style.display = 'block';
        document.getElementById('documentFormContainer').style.display = 'none';
        document.getElementById('documentResultContainer').style.display = 'none';

        // 清除选中状态
        document.querySelectorAll('.document-type-card').forEach(card => {
            card.classList.remove('selected');
        });

        // 重置数据
        this.currentDocumentType = null;
        this.currentFieldGroups = null;
        this.formData = {};
    }

    copyDocumentText() {
        const preview = document.getElementById('documentPreview');
        if (!preview) return;

        navigator.clipboard.writeText(preview.textContent).then(() => {
            this.showSuccess('文书内容已复制到剪贴板');
        }).catch(err => {
            console.error('复制失败:', err);
            this.showError('复制失败，请手动选择文本复制');
        });
    }

    downloadDocument() {
        if (!this.currentResult) return;

        // 显示下载选项菜单
        this.showDownloadOptions();
    }

    showDownloadOptions() {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'block';

        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>选择下载格式</h3>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="download-options">
                        <button class="download-option-btn" data-format="txt">
                            <i class="fas fa-file-alt"></i>
                            <div>
                                <h4>文本文档 (.txt)</h4>
                                <p>纯文本格式，兼容性最好</p>
                            </div>
                        </button>
                        <button class="download-option-btn" data-format="docx">
                            <i class="fas fa-file-word"></i>
                            <div>
                                <h4>Word文档 (.docx)</h4>
                                <p>Microsoft Word格式，支持格式化</p>
                            </div>
                        </button>
                        <button class="download-option-btn" data-format="html">
                            <i class="fas fa-file-code"></i>
                            <div>
                                <h4>网页文档 (.html)</h4>
                                <p>HTML格式，可在浏览器中查看</p>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定下载选项点击事件
        modal.querySelectorAll('.download-option-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const format = btn.dataset.format;
                this.downloadInFormat(format);
                modal.remove();
            });
        });
    }

    downloadInFormat(format) {
        if (!this.currentResult) return;

        const content = this.currentResult.content;
        const baseFilename = `${this.currentResult.document_name}_${new Date().toISOString().split('T')[0]}`;

        switch (format) {
            case 'txt':
                this.downloadAsText(content, `${baseFilename}.txt`);
                break;
            case 'docx':
                this.downloadAsWord(content, `${baseFilename}.docx`);
                break;
            case 'html':
                this.downloadAsHtml(content, `${baseFilename}.html`);
                break;
            default:
                this.downloadAsText(content, `${baseFilename}.txt`);
        }

        this.showSuccess(`文书已下载为 ${format.toUpperCase()} 格式`);
    }

    downloadAsText(content, filename) {
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
        this.triggerDownload(blob, filename);
    }

    downloadAsWord(content, filename) {
        // 创建简单的Word文档格式
        const wordContent = this.formatContentForWord(content);
        const blob = new Blob([wordContent], {
            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        });
        this.triggerDownload(blob, filename);
    }

    downloadAsHtml(content, filename) {
        const htmlContent = this.formatContentForHtml(content);
        const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
        this.triggerDownload(blob, filename);
    }

    formatContentForWord(content) {
        // 创建基本的Word XML格式
        const wordXml = `<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
    <w:body>
        <w:p>
            <w:r>
                <w:t xml:space="preserve">${content.replace(/\n/g, '</w:t></w:r></w:p><w:p><w:r><w:t xml:space="preserve">')}</w:t>
            </w:r>
        </w:p>
    </w:body>
</w:document>`;
        return wordXml;
    }

    formatContentForHtml(content) {
        const title = this.currentResult.document_name;
        const formattedContent = content
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>');

        return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        body {
            font-family: 'SimSun', serif;
            line-height: 1.8;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background: white;
        }
        h1 {
            text-align: center;
            font-size: 24px;
            margin-bottom: 30px;
            color: #333;
        }
        p {
            margin-bottom: 16px;
            text-indent: 2em;
        }
        .document-content {
            white-space: pre-wrap;
        }
        @media print {
            body { margin: 0; padding: 20px; }
        }
    </style>
</head>
<body>
    <h1>${title}</h1>
    <div class="document-content">${formattedContent}</div>
    <script>
        // 自动打印功能
        function printDocument() {
            window.print();
        }
    </script>
</body>
</html>`;
    }

    triggerDownload(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    showError(message) {
        // 使用现有的错误显示机制
        if (window.showError) {
            window.showError(message);
        } else {
            alert(message);
        }
    }

    showSuccess(message) {
        // 使用现有的成功显示机制
        if (window.showSuccess) {
            window.showSuccess(message);
        } else {
            alert(message);
        }
    }
}

// 初始化文书生成器
let documentGenerator;

// 当文档加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 检查是否在文书生成标签页
    const documentTab = document.getElementById('document-tab');
    if (documentTab) {
        documentGenerator = new DocumentGenerator();
    }
});

// 标签页切换时的处理
document.addEventListener('tabChanged', (event) => {
    if (event.detail.tabId === 'document' && !documentGenerator) {
        documentGenerator = new DocumentGenerator();
    }
});
