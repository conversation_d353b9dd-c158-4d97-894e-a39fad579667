// 文书生成功能模块
class DocumentGenerator {
    constructor() {
        this.api = new LanguageMentorAPI();
        this.sessionManager = new SessionManager();
        this.currentDocumentType = null;
        this.currentFieldGroups = null;
        this.formData = {};
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadDocumentTypes();
    }
    
    bindEvents() {
        // 返回选择按钮
        document.getElementById('backToSelector')?.addEventListener('click', () => {
            this.showDocumentSelector();
        });
        
        // 验证表单按钮
        document.getElementById('validateForm')?.addEventListener('click', () => {
            this.validateForm();
        });
        
        // 生成文书按钮
        document.getElementById('generateDocument')?.addEventListener('click', () => {
            this.generateDocument();
        });
        
        // 编辑文书按钮
        document.getElementById('editDocument')?.addEventListener('click', () => {
            this.showDocumentForm();
        });
        
        // 复制文本按钮
        document.getElementById('copyDocument')?.addEventListener('click', () => {
            this.copyDocumentText();
        });
        
        // 下载文档按钮
        document.getElementById('downloadDocument')?.addEventListener('click', () => {
            this.downloadDocument();
        });
    }
    
    async loadDocumentTypes() {
        try {
            showLoading(true);
            const response = await this.api.getDocumentTypes();
            
            if (response.success) {
                this.renderDocumentTypes(response.data);
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('加载文书类型失败:', error);
            this.showError('加载文书类型失败，请刷新页面重试');
        } finally {
            showLoading(false);
        }
    }
    
    renderDocumentTypes(documentTypes) {
        const container = document.getElementById('documentTypes');
        if (!container) return;
        
        container.innerHTML = documentTypes.map(docType => `
            <div class="document-type-card" data-type="${docType.id}">
                <div class="document-type-header">
                    <i class="${docType.icon} document-type-icon"></i>
                    <div>
                        <h4 class="document-type-title">${docType.name}</h4>
                        <div class="document-type-category">${docType.category}</div>
                    </div>
                </div>
                <p class="document-type-description">${docType.description}</p>
                <div class="document-type-meta">
                    <span class="complexity-badge complexity-${this.getComplexityClass(docType.complexity || '中等')}">
                        ${docType.complexity || '中等'}
                    </span>
                    <span class="estimated-time">
                        <i class="fas fa-clock"></i>
                        ${docType.estimated_time || '15-20分钟'}
                    </span>
                </div>
            </div>
        `).join('');
        
        // 绑定点击事件
        container.querySelectorAll('.document-type-card').forEach(card => {
            card.addEventListener('click', () => {
                this.selectDocumentType(card.dataset.type);
            });
        });
    }
    
    getComplexityClass(complexity) {
        const complexityMap = {
            '简单': 'simple',
            '中等': 'medium', 
            '复杂': 'complex'
        };
        return complexityMap[complexity] || 'medium';
    }
    
    async selectDocumentType(documentType) {
        try {
            showLoading(true);
            
            // 更新选中状态
            document.querySelectorAll('.document-type-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-type="${documentType}"]`)?.classList.add('selected');
            
            // 加载字段信息
            const response = await this.api.getDocumentFields(documentType);
            
            if (response.success) {
                this.currentDocumentType = documentType;
                this.currentFieldGroups = response.data.field_groups;
                this.showDocumentForm(response.data);
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('加载文书字段失败:', error);
            this.showError('加载文书字段失败，请重试');
        } finally {
            showLoading(false);
        }
    }
    
    showDocumentForm(data) {
        // 隐藏选择器，显示表单
        document.getElementById('documentSelector').style.display = 'none';
        document.getElementById('documentResultContainer').style.display = 'none';
        document.getElementById('documentFormContainer').style.display = 'block';
        
        if (data) {
            // 更新表单标题和描述
            document.getElementById('selectedDocumentTitle').textContent = data.document_info.name;
            document.getElementById('selectedDocumentDescription').textContent = data.document_info.description;
            
            // 生成表单
            this.renderForm(data.field_groups);
        }
        
        // 重置进度
        this.updateProgress();
    }
    
    renderForm(fieldGroups) {
        const form = document.getElementById('documentForm');
        if (!form) return;
        
        form.innerHTML = fieldGroups.map(group => `
            <div class="form-group">
                <h4 class="form-group-title">
                    <i class="fas fa-folder"></i>
                    ${group.title}
                </h4>
                <div class="form-fields">
                    ${group.fields.map(field => this.renderField(field)).join('')}
                </div>
            </div>
        `).join('');
        
        // 绑定输入事件
        form.querySelectorAll('.field-input').forEach(input => {
            input.addEventListener('input', () => {
                this.updateFormData();
                this.updateProgress();
                this.clearFieldError(input);
            });
            
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
        });
    }
    
    renderField(field) {
        const isRequired = field.required ? 'required' : '';
        const requiredClass = field.required ? 'required' : '';
        
        let inputHtml = '';
        
        switch (field.type) {
            case 'select':
                inputHtml = `
                    <select class="field-input" name="${field.name}" ${isRequired}>
                        <option value="">请选择...</option>
                        ${field.options.map(option => `<option value="${option}">${option}</option>`).join('')}
                    </select>
                `;
                break;
            case 'textarea':
                inputHtml = `
                    <textarea class="field-input" name="${field.name}" 
                              placeholder="${field.placeholder || ''}" 
                              rows="3" ${isRequired}></textarea>
                `;
                break;
            case 'date':
                inputHtml = `
                    <input type="date" class="field-input" name="${field.name}" ${isRequired}>
                `;
                break;
            case 'tel':
                inputHtml = `
                    <input type="tel" class="field-input" name="${field.name}" 
                           placeholder="${field.placeholder || ''}" ${isRequired}>
                `;
                break;
            default:
                inputHtml = `
                    <input type="text" class="field-input" name="${field.name}" 
                           placeholder="${field.placeholder || ''}" ${isRequired}>
                `;
        }
        
        return `
            <div class="field-container">
                <label class="field-label ${requiredClass}">${field.label}</label>
                ${inputHtml}
                <div class="field-error" id="error-${field.name}"></div>
            </div>
        `;
    }
    
    updateFormData() {
        const form = document.getElementById('documentForm');
        if (!form) return;
        
        this.formData = {};
        const formData = new FormData(form);
        
        for (let [key, value] of formData.entries()) {
            this.formData[key] = value.trim();
        }
    }
    
    updateProgress() {
        if (!this.currentFieldGroups) return;
        
        // 计算所有必填字段
        const allRequiredFields = [];
        this.currentFieldGroups.forEach(group => {
            group.fields.forEach(field => {
                if (field.required) {
                    allRequiredFields.push(field.name);
                }
            });
        });
        
        // 计算已填写的必填字段
        const filledRequiredFields = allRequiredFields.filter(fieldName => {
            return this.formData[fieldName] && this.formData[fieldName].length > 0;
        });
        
        const progress = allRequiredFields.length > 0 
            ? Math.round((filledRequiredFields.length / allRequiredFields.length) * 100)
            : 0;
        
        // 更新进度条
        const progressFill = document.getElementById('formProgress');
        const progressText = document.getElementById('progressText');
        
        if (progressFill) progressFill.style.width = `${progress}%`;
        if (progressText) progressText.textContent = `${progress}%`;
        
        // 更新生成按钮状态
        const generateBtn = document.getElementById('generateDocument');
        if (generateBtn) {
            generateBtn.disabled = progress < 100;
        }
    }
    
    validateField(input) {
        const field = this.getFieldConfig(input.name);
        if (!field) return true;
        
        let isValid = true;
        let errorMessage = '';
        
        // 检查必填字段
        if (field.required && !input.value.trim()) {
            isValid = false;
            errorMessage = `${field.label}是必填项`;
        }
        
        // 检查正则验证
        if (isValid && field.validation && input.value.trim()) {
            const regex = new RegExp(field.validation);
            if (!regex.test(input.value.trim())) {
                isValid = false;
                errorMessage = `${field.label}格式不正确`;
            }
        }
        
        // 显示错误信息
        if (!isValid) {
            this.showFieldError(input.name, errorMessage);
            input.classList.add('error');
        } else {
            this.clearFieldError(input);
            input.classList.remove('error');
        }
        
        return isValid;
    }
    
    getFieldConfig(fieldName) {
        if (!this.currentFieldGroups) return null;
        
        for (let group of this.currentFieldGroups) {
            for (let field of group.fields) {
                if (field.name === fieldName) {
                    return field;
                }
            }
        }
        return null;
    }
    
    showFieldError(fieldName, message) {
        const errorElement = document.getElementById(`error-${fieldName}`);
        if (errorElement) {
            errorElement.textContent = message;
        }
    }
    
    clearFieldError(input) {
        const errorElement = document.getElementById(`error-${input.name}`);
        if (errorElement) {
            errorElement.textContent = '';
        }
    }
    
    async validateForm() {
        try {
            showLoading(true);
            this.updateFormData();
            
            // 前端验证
            const form = document.getElementById('documentForm');
            const inputs = form.querySelectorAll('.field-input');
            let isValid = true;
            
            inputs.forEach(input => {
                if (!this.validateField(input)) {
                    isValid = false;
                }
            });
            
            if (!isValid) {
                this.showError('请修正表单中的错误信息');
                return;
            }
            
            // 后端验证
            const response = await this.api.validateDocumentData(this.currentDocumentType, this.formData);
            
            if (response.success) {
                if (response.data.is_valid) {
                    this.showSuccess('信息验证通过，可以生成文书');
                    document.getElementById('generateDocument').disabled = false;
                } else {
                    this.showError(response.data.message);
                }
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('验证失败:', error);
            this.showError('验证失败，请重试');
        } finally {
            showLoading(false);
        }
    }

    async generateDocument() {
        try {
            showLoading(true);
            this.updateFormData();

            const sessionId = this.sessionManager.getSessionId('document', this.currentDocumentType);
            const response = await this.api.generateDocument(this.currentDocumentType, this.formData, sessionId);

            if (response.success) {
                this.showDocumentResult(response);
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('生成文书失败:', error);
            this.showError('生成文书失败，请重试');
        } finally {
            showLoading(false);
        }
    }

    showDocumentResult(result) {
        // 隐藏表单，显示结果
        document.getElementById('documentFormContainer').style.display = 'none';
        document.getElementById('documentResultContainer').style.display = 'block';

        // 显示生成的文书内容
        const preview = document.getElementById('documentPreview');
        if (preview) {
            preview.textContent = result.content;
        }

        // 保存结果数据
        this.currentResult = result;
    }

    showDocumentSelector() {
        // 显示选择器，隐藏其他
        document.getElementById('documentSelector').style.display = 'block';
        document.getElementById('documentFormContainer').style.display = 'none';
        document.getElementById('documentResultContainer').style.display = 'none';

        // 清除选中状态
        document.querySelectorAll('.document-type-card').forEach(card => {
            card.classList.remove('selected');
        });

        // 重置数据
        this.currentDocumentType = null;
        this.currentFieldGroups = null;
        this.formData = {};
    }

    copyDocumentText() {
        const preview = document.getElementById('documentPreview');
        if (!preview) return;

        navigator.clipboard.writeText(preview.textContent).then(() => {
            this.showSuccess('文书内容已复制到剪贴板');
        }).catch(err => {
            console.error('复制失败:', err);
            this.showError('复制失败，请手动选择文本复制');
        });
    }

    downloadDocument() {
        if (!this.currentResult) return;

        const content = this.currentResult.content;
        const filename = `${this.currentResult.document_name}_${new Date().toISOString().split('T')[0]}.txt`;

        // 创建下载链接
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showSuccess('文书已下载');
    }

    showError(message) {
        // 使用现有的错误显示机制
        if (window.showError) {
            window.showError(message);
        } else {
            alert(message);
        }
    }

    showSuccess(message) {
        // 使用现有的成功显示机制
        if (window.showSuccess) {
            window.showSuccess(message);
        } else {
            alert(message);
        }
    }
}

// 初始化文书生成器
let documentGenerator;

// 当文档加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 检查是否在文书生成标签页
    const documentTab = document.getElementById('document-tab');
    if (documentTab) {
        documentGenerator = new DocumentGenerator();
    }
});

// 标签页切换时的处理
document.addEventListener('tabChanged', (event) => {
    if (event.detail.tabId === 'document' && !documentGenerator) {
        documentGenerator = new DocumentGenerator();
    }
});
